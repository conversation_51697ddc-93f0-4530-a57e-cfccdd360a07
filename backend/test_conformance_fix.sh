#!/bin/bash

# 测试脚本：验证PQL连接图修复
# 用于测试ConformanceService中的PQL连接问题修复

echo "=== 测试ConformanceService PQL连接图修复 ==="
echo "时间: $(date)"
echo ""

# 设置测试参数
API_URL="http://localhost:8889/api/engine/sp-engine/getConformanceGeneralData"
AUTH_TOKEN="1eeb48f3-549e-46b9-a2fe-b06db3b795cc"
TOPIC_ID="67"
TOPIC_SHEET_ID="767"

echo "测试参数:"
echo "  API URL: $API_URL"
echo "  Topic ID: $TOPIC_ID"
echo "  Topic Sheet ID: $TOPIC_SHEET_ID"
echo ""

# 构建请求数据
REQUEST_DATA='{
  "topicId":"'$TOPIC_ID'",
  "topicSheetId":"'$TOPIC_SHEET_ID'",
  "applyTopicFilter":true
}'

echo "请求数据:"
echo "$REQUEST_DATA"
echo ""

# 执行测试请求
echo "=== 执行测试请求 ==="
echo "发送请求到: $API_URL"

RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}\nTIME_TOTAL:%{time_total}" \
  -H 'Content-Type: application/json;charset=UTF-8' \
  -H "Authorization: $AUTH_TOKEN" \
  --data-raw "$REQUEST_DATA" \
  "$API_URL")

# 解析响应
HTTP_CODE=$(echo "$RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
TIME_TOTAL=$(echo "$RESPONSE" | grep "TIME_TOTAL:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | sed '/HTTP_CODE:/d' | sed '/TIME_TOTAL:/d')

echo ""
echo "=== 测试结果 ==="
echo "HTTP状态码: $HTTP_CODE"
echo "响应时间: ${TIME_TOTAL}秒"
echo ""

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ 测试成功！API请求返回200状态码"
    echo ""
    echo "响应内容:"
    echo "$RESPONSE_BODY" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE_BODY"
    
    # 检查响应是否包含预期的数据结构
    if echo "$RESPONSE_BODY" | grep -q "conformanceCaseNum\|conformanceRate"; then
        echo ""
        echo "✅ 响应包含预期的conformance数据结构"
    else
        echo ""
        echo "⚠️  响应不包含预期的conformance数据结构"
    fi
    
elif [ "$HTTP_CODE" = "500" ]; then
    echo "❌ 测试失败！服务器内部错误 (500)"
    echo ""
    echo "错误响应:"
    echo "$RESPONSE_BODY"
    
    # 检查是否仍然是PQL连接图错误
    if echo "$RESPONSE_BODY" | grep -q "No path found\|PQLJoinGraph"; then
        echo ""
        echo "❌ 仍然存在PQL连接图错误，修复未生效"
        echo "建议检查:"
        echo "  1. 服务是否已重启"
        echo "  2. 代码修改是否已编译"
        echo "  3. 表连接关系是否正确建立"
    else
        echo ""
        echo "❌ 出现了其他类型的服务器错误"
    fi
    
else
    echo "❌ 测试失败！HTTP状态码: $HTTP_CODE"
    echo ""
    echo "响应内容:"
    echo "$RESPONSE_BODY"
fi

echo ""
echo "=== 测试完成 ==="
echo "时间: $(date)"

# 如果测试失败，提供故障排除建议
if [ "$HTTP_CODE" != "200" ]; then
    echo ""
    echo "=== 故障排除建议 ==="
    echo "1. 检查应用服务是否正在运行:"
    echo "   ps aux | grep java | grep process-mining"
    echo ""
    echo "2. 检查应用日志中的错误信息:"
    echo "   tail -f logs/application.log | grep -i error"
    echo ""
    echo "3. 验证数据库连接和表是否存在:"
    echo "   - conformance_db.reason_$TOPIC_SHEET_ID"
    echo "   - conformance表"
    echo ""
    echo "4. 如果仍有PQL连接错误，检查表注册逻辑:"
    echo "   - registerTempPuView调用"
    echo "   - variantId字段连接"
    echo "   - 表间连接路径"
fi
