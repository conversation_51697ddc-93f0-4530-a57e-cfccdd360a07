package com.prx.service.conformance

import java.math.{BigDecimal, RoundingMode}
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util
import java.util.concurrent.TimeUnit
import java.util.stream.Collectors
import java.util.{List, Objects, UUID}
import com.google.common.cache.{<PERSON><PERSON><PERSON><PERSON><PERSON>, CacheLoader}
import com.prx.commons.utils.BpmnUtilsPy
import com.prx.service.DimensionQuery
import com.prx.service.conformance.ConformanceService.{CONFORMANCE_DB_NAME, reasonTableName}
import com.prx.service.overview.ProcessOverview.isEmpty
import com.sp.proxverse.common.UserInfoUtil
import com.sp.proxverse.common.model.dict.conformance.UnConformanceEnum
import com.sp.proxverse.common.model.dict.prehandler.PreHandlerVariableEnum
import com.sp.proxverse.common.model.dict.{KpiFromKnowledgeEnum, KpiSaveTypeEnum, KpiUpDownEnum, ReasonTypeEnum}
import com.sp.proxverse.common.model.dto.conformance._
import com.sp.proxverse.common.model.dto.{ConformanceKipDTO, DataModelFileDTO, RelatedDTO}
import com.sp.proxverse.common.model.po.{AllowProcessPO, TopicSheetPO}
import com.sp.proxverse.common.model.vo.conformance.ConformanceViewDataOutputVO
import com.sp.proxverse.common.util.DataUtil
import com.sp.proxverse.interfaces.dao.service.{AllowProcessService, CloudFileInfoService, TopicSheetKpiService, TopicSheetService}
import com.sp.proxverse.interfaces.service.data.DataAPIService
import com.sp.proxverse.interfaces.service.data.pql.{PQLParameterManager, PQLService, TopicFilterManager}
import org.apache.spark.internal.Logging
import org.apache.spark.sql.catalyst.analysis.UnresolvedAttribute
import org.apache.spark.sql.catalyst.expressions.Literal
import org.apache.spark.sql.datasource.DataSource
import org.apache.spark.sql.functions._
import org.apache.spark.sql.pql.expressions.Conformance
import org.apache.spark.sql.pql.expressions.conformance.PetriNet
import org.apache.spark.sql.pql.manager.CatalogManager
import org.apache.spark.sql.pql.{PQLBuilder, PQLFunctions}
import org.apache.spark.sql.{Column, SparkSessionEnv}
import org.apache.spark.unsafe.types.UTF8String
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import scala.collection.JavaConverters._
import scala.collection.Seq
import scala.collection.parallel.ParSeq


case class ConformanceCacheKey(
    topicID: Int,
    sheetID: Int,
    filter: String,
    timeRangeFilter: String)

case class ReasonCacheKey(reason: String, reasonType: Integer)

case class DiagramCacheKey(reason: String, reasonType: Integer, timeFormat: String)

/**
 *
 * @param name
 * @param conformanceFilter
 * @param unconformanceFilter
 * @param conformanceFilterSingle
 * @param unconformanceFilterSingle
 * @param exists true：表示存在的reason(包括condition)，false：表示不存在的reason(不参与计算)
 */
case class ConditionFilter(
    name: String,
    conformanceFilter: String,
    unconformanceFilter: String,
    conformanceFilterSingle: String,
    unconformanceFilterSingle: String,
    exists: Boolean)

@Service
class ConformanceServiceScala extends Logging {

  private case class ConformanceResultCache(topicID: Int, sheetID: Int, timeRangeFilter: String)
      extends Logging {

    private val reasonResultCache = CacheBuilder.newBuilder
      .maximumSize(100)
      .expireAfterAccess(6, TimeUnit.HOURS)
      .build(new CacheLoader[DiagramCacheKey, ConformanceViewDataOutputVO]() {
        @throws[Exception]
        override def load(reason: DiagramCacheKey): ConformanceViewDataOutputVO =
          mkConformanceViewData(
            topicID,
            sheetID,
            reason.reason,
            reason.reasonType,
            reason.timeFormat)
      })

    lazy val unConformance = makeConformanceVariantList(
      ConformanceCacheKey(topicID, sheetID, null, timeRangeFilter))

    lazy val kpi = mkConformanceKpi4General(topicID, sheetID, timeRangeFilter)

    lazy val conformanceGeneralData =
      mkConformanceGeneralData(topicID, sheetID, timeRangeFilter)

    def conformanceViewData(reason: DiagramCacheKey): ConformanceViewDataOutputVO = {
      reasonResultCache.get(reason)
    }

    def mkConformanceViewData(
        topicID: Int,
        sheetID: Int,
        filterReason: String,
        reasonType: Integer,
        timeFormat: String): ConformanceViewDataOutputVO = {
      val (reason, replaceAllowProcess) = if (filterReason.equals("null")) {
        val table = conformanceTableCache.getUnchecked(sheetID)
        (s"${table}.isConforms", true)
      } else if (ReasonTypeEnum.isConditionReason(reasonType)) {
        (filterReason, false)
      } else {
        val table = reasonTableCache.getUnchecked(sheetID)
        (s"${table}.reason = '${filterReason}'", false)
      }
      val conditionFilters = buildConditionFilterExpression(topicID, sheetID)
      val reasonBuild = createBuilder(topicID, sheetID, replaceAllowProcess)
      reasonBuild.addTopicFilter(reason)
      conditionFilters.foreach(tp => reasonBuild.addTopicFilter(tp.conformanceFilter))
      reasonBuild.addTopicFilter(timeRangeFilter)
      reasonBuild.addColumn(
        s"date_format(`case_table`.${PreHandlerVariableEnum.START_TIME.getValue}, '${timeFormat}')")
      reasonBuild.addColumn(s"count( `case_table`.${PreHandlerVariableEnum.CASE_ID.getValue})")
      val reasonResult = reasonBuild.collect()

      val totalBuild = createBuilder(topicID, sheetID)
      totalBuild.addColumn(
        s"date_format(`case_table`.${PreHandlerVariableEnum.START_TIME.getValue}, '${timeFormat}')",
        "date")
      totalBuild.addColumn(s"count( `case_table`.${PreHandlerVariableEnum.CASE_ID.getValue})")
      totalBuild.addTopicFilter(timeRangeFilter)
      totalBuild.addSort("date", 1)

      val totalCount = totalBuild.collect()

      val reasonCount = reasonResult.map { row =>
        val x = if (row.isNullAt(0)) {
          "null"
        } else {
          row.getString(0)
        }
        val y = row.getLong(1)
        (x, y)
      }.toMap

      val result = totalCount.map { row =>
        val x = if (row.isNullAt(0)) {
          "null"
        } else {
          row.getString(0)
        }
        val y = if (!reasonCount.contains(x)) {
          BigDecimal.ZERO.toString
        } else {
          val reason = reasonCount.apply(x)
          BigDecimal
            .valueOf(reason)
            .divide(BigDecimal.valueOf(row.getLong(1)), 4, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100))
            .setScale(2)
            .toString
        }
        (x, y)
      }
      val completedDates = buildConformanceViewDataCompleted(result, timeFormat)
      ConformanceViewDataOutputVO
        .builder()
        .xdata(completedDates.map(_._1).toList.asJava)
        .ydata(completedDates.map(_._2).toList.asJava)
        .build()
    }

    def buildConformanceViewDataCompleted(
        result: Array[(String, String)],
        timeFormat: String): Array[(String, String)] = {
      val map = result.toMap
      val dates =
        ConformanceService.completedDate(result.filter(_._1 != "null").map(_._1), timeFormat)
      dates.map(m => (m, map.getOrElse(m, "100")))
    }

    def mkConformanceGeneralData(
        topicID: Int,
        sheetID: Int,
        timeRangeFilter: String): ConformanceGeneralData = {
      try {
        val conformanceTableName = conformanceTableCache.get(sheetID)
        val reasonTableName = reasonTableCache.get(sheetID)

        logInfo(s"Starting mkConformanceGeneralData for topicID=$topicID, sheetID=$sheetID")
        logInfo(s"Using conformanceTable=$conformanceTableName, reasonTable=$reasonTableName")

        // 先计算一致，再用`total` - `一致` = `不一致`
        val conditionFilters = buildConditionFilterExpression(topicID, sheetID)
        val conformanceBuilder = createBuilder(topicID, sheetID)
          .addColumn(s"count(`case_table`.${PreHandlerVariableEnum.CASE_ID.getValue})")
          .addTopicFilter(s"$conformanceTableName.isConforms")
        conformanceBuilder.addTopicFilter(timeRangeFilter)
        conditionFilters.foreach(tp => conformanceBuilder.addTopicFilter(tp.conformanceFilter))
        val conformanceData = conformanceBuilder.collect().head.getLong(0)

        val totalBuilder = pqlService
          .newPQLBuilder(topicID, sheetID)
          .addColumn("count(ENCODED_CASE_CASE_ID_COLUMN())")
        totalBuilder.addTopicFilter(timeRangeFilter)
        val totalCase = totalBuilder.collect().head.getLong(0)
        val unconformanceData = totalCase.toLong - conformanceData

        val allwoList = allowProcessService.getList(sheetID)
        val builder = createBuilder(topicID, sheetID)
        builder.addColumn(s"count(distinct $reasonTableName.reason)")
        builder.addTopicFilter(s"$reasonTableName.reason != 'Conforms'")
        builder.addTopicFilter(timeRangeFilter)
        val allows = allwoList.asScala
          .filter(_.getReasonType == 1)
          .map(m => s"'${m.getResult}'")
        if (allows.nonEmpty) {
          builder.addTopicFilter(s"$reasonTableName.reason not in (${allows.mkString(",")})")
        }

//        logInfo(s"Executing query to get unallow count with filters: ${builder.getTopicFilters}")
        val unallow = builder.collect().head.getLong(0) + mkConditionReasonUnConformanceCount(
          conditionFilters,
          allwoList)
      } catch {
        case e: RuntimeException if e.getMessage.contains("No path found") =>
          logError(s"PQL connection path error in mkConformanceGeneralData: ${e.getMessage}")
          logError(s"TopicID: $topicID, SheetID: $sheetID")
          logError(s"ConformanceTable: ${conformanceTableCache.get(sheetID)}")
          logError(s"ReasonTable: ${reasonTableCache.get(sheetID)}")
          throw new RuntimeException(s"Failed to establish connection between tables for sheet $sheetID. " +
            s"Please check table registration and connection paths.", e)
        case e: Exception =>
          logError(s"Unexpected error in mkConformanceGeneralData: ${e.getMessage}", e)
          throw e
      }

      ConformanceGeneralData
        .builder()
        .conformanceCaseNum(conformanceData.toString)
        .unConformanceCaseNum(unconformanceData.toString)
        .conformanceRate(if (conformanceData == 0) {
          "0"
        } else {
          new BigDecimal(conformanceData)
            .divide(new BigDecimal(conformanceData + unconformanceData), 4, RoundingMode.HALF_UP)
            .multiply(new BigDecimal(100))
            .stripTrailingZeros()
            .toPlainString
        })
        .allowVariantNum(allwoList.size().toString)
        .unAllowVariantNum(unallow.toString)
        .build()
    }

    def mkConditionReasonUnConformanceCount(
        conditionFilters: Array[ConditionFilter],
        allwoList: util.List[AllowProcessPO]): Int = {
      val conditionMap = conditionCache
        .getUnchecked(sheetID)
        .map(m => (m.name, m))
        .toMap
      val token = userInfoUtil.getUserInfo
      conditionFilters
        .filter(
          f =>
            !allwoList.asScala
              .filter(f => ReasonTypeEnum.isConditionReason(f.getReasonType))
              .map(_.getConditionName)
              .contains(f.name))
        .par
        .filterNot(f => {
          UserInfoUtil.authInfo.set(token)
          val conditionResaon = conditionMap.getOrElse(f.name, null)
          val tuple = bpmnConditionService.calcUnConformanceCaseNumWithCondition(
            topicID,
            sheetID,
            timeRangeFilter,
            conditionResaon)
          tuple._1 == 0L
        })
        .length
    }

    def mkConformanceKpi4General(
        topicID: Int,
        sheetID: Int,
        timeRangeFilter: String): java.util.List[ConformanceKpi] = {
      try {
        val conformanceTableName = conformanceTableCache.get(sheetID)
        getConformanceKpi4General(
          topicID,
          sheetID,
          null,
          s"${conformanceTableName}.isConforms",
          s"not ${conformanceTableName}.isConforms",
          timeRangeFilter)
      } catch {
        case e: Throwable =>
          logError(s"Error for compute kpi", e)
          topicSheetKpiService
            .getTopicSheetKpiList(sheetID)
            .asScala
            .par
            .map { kpi =>
              val builder = ConformanceKpi.builder
                .unit(dataApiClient.formatToPQL(topicID, sheetID, kpi.getUnit))
                .name(dataApiClient.formatRefer(topicID, kpi.getName))
                .originalName(kpi.getName)
                .originalUnit(kpi.getUnit)
                .kpiId(kpi.getId)
                .baseLine(kpi.getBaseLine)
                .kpiType(kpi.getType)
                .expression(kpi.getExpression)
                .topicSheetId(sheetID)
                .saveType(
                  if (Objects.equals(kpi.getFromKnowledge, KpiFromKnowledgeEnum.FROM.getValue))
                    KpiSaveTypeEnum.KNOWLEDGE_MODEL.getValue
                  else KpiSaveTypeEnum.CUSTOMIZE.getValue)
                .initType(kpi.getType)
                .formatting(kpi.getFormatting)
                .format(kpi.getFormat)
                .columnType(kpi.getColumnType)
              builder
                .errorMsg(e.getMessage)
                .build()
            }
            .toList
            .asJava
      }
    }

    def buildUnConformanceObject(
        sheetKpiList: ParSeq[Map[String, ConformanceKipDTO]],
        reason: String,
        i18InReason: String,
        caseNum: Long,
        totalCase: Long,
        reasonType: Int,
        multi: BigDecimal): UnConformance = {
      // sheetKpiList 值是中文，reason 是英文，i18InReason 是国际化语言，所以将 reason 转换成i18InReason 与 sheetKpiList 匹配
      val kpiList = sheetKpiList
        .filter(_.contains(UnConformanceEnum.to18InResult(reason)))
        .map(map => map.apply(UnConformanceEnum.to18InResult(reason)))
        .toList
        .asJava
      UnConformance
        .builder()
        .resultOrigin(reason)
        .result(i18InReason)
        .caseNum(caseNum)
        .caseRate(
          if (totalCase == 0) BigDecimal.ZERO
          else
            BigDecimal
              .valueOf(caseNum)
              .divide(BigDecimal.valueOf(totalCase), 4, RoundingMode.HALF_UP)
              .multiply(multi))
        .kipDTOList(kpiList)
        .reasonType(reasonType)
        .build()
    }

    def makeConformanceVariantList(cacheKey: ConformanceCacheKey): ConformanceCache = {
      val conformanceVariantList = new util.ArrayList[String]
      val reasonTable: String = reasonTableCache.getUnchecked(cacheKey.sheetID)
      val conformanceTable: String = conformanceTableCache.getUnchecked(cacheKey.sheetID)
      val conditionFilters =
        buildConditionFilterExpression(cacheKey.topicID, cacheKey.sheetID, false)
      val sheetKpiList = calcKPI(cacheKey, reasonTable, conformanceTable, conditionFilters)
      val allowList = allowProcessService.getList(cacheKey.sheetID).asScala
      val allowReason = allowList.map(_.getResult)

      val totalCase = pqlService.calcExpression(
        cacheKey.topicID,
        cacheKey.sheetID,
        "count(ENCODED_CASE_CASE_ID_COLUMN())")
      val conformance = createBuilder(cacheKey.topicID, cacheKey.sheetID, false)
      conformance.addColumn(s"${reasonTable}.reason")
      conformance.addColumn(s"count(`case_table`.`${PreHandlerVariableEnum.CASE_ID.getValue}`)")
      conformance.addTopicFilter(cacheKey.timeRangeFilter)
      val conformanceRows = conformance
        .collect()
        .filterNot(row => row.isNullAt(0) || row.getString(0).equals("Conforms"))
      val multi = new BigDecimal(100)
      val conformances = conformanceRows
        .map { tp_row =>
          val reason = tp_row.getString(0)
          val i18InReason = UnConformanceEnum.to18InResult(reason)
          // 有的 join 之后 可能这个原因找不到了. 比如 但是activity 但是 case表没有
          buildUnConformanceObject(
            sheetKpiList,
            reason,
            i18InReason,
            tp_row.getLong(1),
            totalCase.toLong,
            ReasonTypeEnum.PETRINET_REASON.getValue,
            multi)
        }
      // 计算、拼接condition reason
      val conditionReasonTuples = conditionCache.getUnchecked(sheetID)
      val token = userInfoUtil.getUserInfo
      val conditionReasons = conditionReasonTuples.par
        .map(conditionResaon => {
          UserInfoUtil.authInfo.set(token)
          val i18InReason = conditionResaon.name
          val tuple = bpmnConditionService.calcUnConformanceCaseNumWithCondition(
            topicID,
            sheetID,
            timeRangeFilter,
            conditionResaon)
          buildUnConformanceObject(
            sheetKpiList,
            tuple._2,
            i18InReason,
            tuple._1,
            totalCase.toLong,
            ReasonTypeEnum.CONDITION_REASON.getValue,
            multi)
        })
        .filterNot(f => f.getCaseNum == 0L)

      val conformancesAndConditions = conformances ++ conditionReasons

      val (allow, unconformance) =
        conformancesAndConditions.partition(p => allowReason.contains(p.getResultOrigin))
      ConformanceCache.builder
        .unConformanceList(unconformance.sortBy(_.getCaseRate).reverse.toList.asJava)
        .conformanceVariantList(conformanceVariantList)
        .allowUnconformanceList(
          mkAllowList(topicID, sheetID, allowList.toArray, allow)
            .sortBy(_.getCaseRate)
            .reverse
            .toList
            .asJava)
        .build
    }

    def mkAllowList(
        topicID: Integer,
        sheetID: Integer,
        allowList: Array[AllowProcessPO],
        allowCalculated: Array[UnConformance]): Array[UnConformance] = {
      val allowMap = allowCalculated.map(m => (m.getResultOrigin, m)).toMap
      val emptyKpiList = topicSheetKpiService
        .getTopicSheetKpiList(sheetID)
        .asScala
        .map(kpi => {
          val kpiName = pQLParameterManager.substituteExprFormat(topicID, kpi.getName)
          val kpiUnit = pQLParameterManager.substituteExprFormat(topicID, kpi.getUnit)
          ConformanceKipDTO
            .builder()
            .kpiName(kpiName)
            .kpiValue("--")
            .upDown(KpiUpDownEnum.NONE.getValue)
            .unit(kpiUnit)
            .format(kpi.getFormat)
            .formatting(kpi.getFormatting)
            .columnType(kpi.getColumnType)
            .build()
        })
        .toList
        .asJava
      allowList.map(reason => {
        val allowData = allowMap.getOrElse(reason.getResult, null)
        if (allowData != null) {
          allowData
        } else {
          UnConformance
            .builder()
            .resultOrigin(reason.getResult)
            .result(UnConformanceEnum.to18InResult(reason.getResult))
            .caseNum(0L)
            .caseRate(BigDecimal.ZERO)
            .kipDTOList(emptyKpiList)
            .reasonType(reason.getReasonType)
            .build()
        }
      })
    }

    private def calcKPI(
        cacheKey: ConformanceCacheKey,
        reasonTable: String,
        conformanceTable: String,
        conditionFilters: Array[ConditionFilter]) = {
      val token = userInfoUtil.getUserInfo
      topicSheetKpiService
        .getTopicSheetKpiList(cacheKey.sheetID)
        .asScala
        .par
        .map { kpi =>
          UserInfoUtil.authInfo.set(token)
          val kpiName = pQLParameterManager.substituteExprFormat(cacheKey.topicID, kpi.getName)
          val kpiUnit = pQLParameterManager.substituteExprFormat(cacheKey.topicID, kpi.getUnit)
          val expression = buildKpiExpressionWithFirst(topicID, sheetID, kpi.getExpression)
          var kpiBuilder = createBuilder(cacheKey.topicID, cacheKey.sheetID, false)
          kpiBuilder.addColumn(s"${reasonTable}.reason")
          kpiBuilder.addColumn(expression)
          kpiBuilder.addTopicFilter(cacheKey.timeRangeFilter)
          val tuples = kpiBuilder
            .collect()
            .filterNot(_.isNullAt(0))
            .map { row =>
              (
                UnConformanceEnum.to18InResult(row.getString(0)),
                if (row.isNullAt(1)) "" else row.get(1).toString)
            }
          val (ignore, unconforms) = tuples
            .partition(
              _._1.equals(UnConformanceEnum.to18InResult(UnConformanceEnum.CONFORMANCE.getName)))

          val conditionNameValues =
            calcConditionKpi(topicID, sheetID, conditionFilters, kpi.getExpression)

          val unconformsAndCondition = unconforms ++ conditionNameValues

          kpiBuilder = createBuilder(cacheKey.topicID, cacheKey.sheetID)
          kpiBuilder.addColumn(expression)
          kpiBuilder.addTopicFilter(s"${conformanceTable}.isConforms")
          conditionFilters
            .filter(_.exists)
            .foreach(tp => kpiBuilder.addTopicFilter(tp.conformanceFilter))
          kpiBuilder.addTopicFilter(cacheKey.timeRangeFilter)
          val conformanceKPI = kpiBuilder
            .collect()
            .head
            .get(0)

          val kPIMap = unconformsAndCondition.map { tp =>
            val unAllowKpiDto = new ConformanceKipDTO
            unAllowKpiDto.setKpiValue(tp._2)
            unAllowKpiDto.setKpiName(kpiName)
            unAllowKpiDto.setUnit(kpiUnit)
            val conformanceKpiValue =
              if (conformanceKPI == null) "0"
              else conformanceKPI.toString
            if (DataUtil.isNumeric(tp._2) && DataUtil.isNumeric(conformanceKpiValue)) {
              val kpiValue = new BigDecimal(tp._2)
              val subtract = kpiValue.subtract(new BigDecimal(conformanceKpiValue))
              unAllowKpiDto.setUpDownValue(subtract.abs.setScale(2, RoundingMode.HALF_UP))
              unAllowKpiDto.setUpDown(ConformanceService.calUpDown(subtract))
            } else {
              unAllowKpiDto.setUpDownValue(null)
              unAllowKpiDto.setUpDown(KpiUpDownEnum.NONE.getValue)
            }
            unAllowKpiDto.setFormat(kpi.getFormat)
            unAllowKpiDto.setFormatting(kpi.getFormatting)
            unAllowKpiDto.setColumnType(kpi.getColumnType)
            (tp._1, unAllowKpiDto)
          }.toMap

          kPIMap
        }
    }
  }

  @Autowired
  var topicSheetService: TopicSheetService = _

  @Autowired
  var cloudFileInfoService: CloudFileInfoService = _

  @Autowired
  var allowProcessService: AllowProcessService = _

  @Autowired
  var pqlService: PQLService = _

  @Autowired
  var topicSheetKpiService: TopicSheetKpiService = _

  @Autowired
  var topicFilterManager: TopicFilterManager = _

  @Autowired
  var userInfoUtil: UserInfoUtil = _

  @Autowired
  var pQLParameterManager: PQLParameterManager = _
  @Autowired
  private var dataApiClient: DataAPIService = _

  @Autowired
  var pQLCatalogManager: CatalogManager = _

  @Autowired
  var bpmnConditionService: BpmnConditionService = _

  val reasonTableCache = CacheBuilder.newBuilder
    .maximumSize(100)
    .expireAfterAccess(6, TimeUnit.HOURS)
    .build(new CacheLoader[Integer, String]() {
      @throws[Exception]
      override def load(sheetID: Integer): String = createReasonTable(sheetID)
    })

  private val conditionCache = CacheBuilder.newBuilder
    .maximumSize(100)
    .expireAfterAccess(6, TimeUnit.HOURS)
    .build(new CacheLoader[Integer, Array[BpmnConditionExpression]]() {
      @throws[Exception]
      override def load(key: Integer): Array[BpmnConditionExpression] =
        bpmnConditionService.queryConditionExpressionRows(key)
    })

  def deleteReasonTable(sheetID: Int): Unit = {
    // 因为可能之前没删除 所以这是 seq
    ConformanceService.reasonTableName(sheetID).foreach(DataSource.deleteTable)
  }

  def deleteConformanceTable(sheetID: Int): Unit = {
    DataSource.deleteTable(ConformanceService.conformanceTableName(sheetID))
  }

  private val conformanceTableCache = CacheBuilder.newBuilder
    .maximumSize(100)
    .expireAfterAccess(6, TimeUnit.HOURS)
    .build(new CacheLoader[Integer, String]() {
      @throws[Exception]
      override def load(sheetID: Integer): String = createConformanceTable(sheetID)
    })

  private val resultCache = CacheBuilder.newBuilder
    .maximumSize(100)
    .expireAfterAccess(6, TimeUnit.HOURS)
    .build(new CacheLoader[ConformanceCacheKey, ConformanceResultCache]() {
      @throws[Exception]
      override def load(key: ConformanceCacheKey): ConformanceResultCache =
        ConformanceResultCache(key.topicID, key.sheetID, key.timeRangeFilter)
    })

  def invalidAll(): Unit = {
    resultCache.invalidateAll()
    conformanceTableCache.invalidateAll()
    reasonTableCache.invalidateAll()
    conditionCache.invalidateAll()
    bpmnConditionService.invalidAll()
    SparkSessionEnv.getSparkSession.catalog
      .listTables(CONFORMANCE_DB_NAME)
      .collect()
      .map(tb => s"${CONFORMANCE_DB_NAME}.`${tb.name}`")
      .foreach(DataSource.deleteTable)
  }

  def invalidConformanceTable(sheetID: Int): Unit = {
    conformanceTableCache.invalidate(sheetID)
    DataSource.deleteTable(ConformanceService.conformanceTableName(sheetID))
    invalidDataCache(sheetID)
  }

  def invalidDataCache(sheetID: Int): Unit = {
    val keys = resultCache
      .asMap()
      .asScala
      .keys
      .filter(_.sheetID == sheetID)
    keys
      .foreach(v => resultCache.invalidate(v))
    bpmnConditionService.invalidBySheetId(sheetID)
  }

  // 清除此topic下的一致性缓存
  def invalidTopicCache(topicId: Int): Unit = {
    topicSheetService.getList(topicId).asScala
        .map(_.getId.toInt)
        .foreach(invalidSheetData)
  }


  def invalidSheetData(sheetID: Int): Unit = {
    conformanceTableCache.invalidate(sheetID)
    deleteConformanceTable(sheetID)
    reasonTableCache.invalidate(sheetID)
    deleteReasonTable(sheetID)
    conditionCache.invalidate(sheetID)
    invalidDataCache(sheetID)
    bpmnConditionService.invalidBySheetId(sheetID)
  }

  def removeConditionReason(sheeetId: Int): Unit = {
    allowProcessService.removeConditionReason(sheeetId)
  }

  def getConformanceVariants(sheetID: Int, isNot: Boolean = false): java.util.List[String] = {
    val conformanceTable = conformanceTableCache.get(sheetID)
    val fileBySheetId = topicSheetService.getTopicSheetFileBySheetId(sheetID)
    if (Objects.isNull(fileBySheetId)) return null
    val builder = createBuilder(fileBySheetId.getTopicId, sheetID)
    builder.addColumn(s"${conformanceTable}.variant")
    builder.addColumn("count(*)")
    builder.addTopicFilter(s"${if (isNot) "NOT" else ""} ${conformanceTable}.isConforms")
    builder.collect().map(_.getString(0)).toList.asJava
  }

  def getConformanceVariantIDs(topicID: Int, sheetID: Int): Array[Int] = {
    val conformanceTable = conformanceTableCache.get(sheetID)
    val conditionFilters = buildConditionFilterExpression(topicID, sheetID)
    val builder = createBuilder(topicID, sheetID)
      .addColumn(s"$conformanceTable.variantId")
      .addColumn("count(*)")
      .addTopicFilter(s"$conformanceTable.isConforms")
    conditionFilters.foreach(tp => builder.addTopicFilter(tp.conformanceFilter))
    builder
      .collect()
      .map(_.getInt(0))
  }

  def mkConformanceFilter(topicId: Int, sheetId: Int): String = {
    val conformanceVariantIds = getConformanceVariantIDs(topicId, sheetId)
    if (conformanceVariantIds.length == 0) {
      null
    } else {
      s"`case_table`.`${PreHandlerVariableEnum.VARIANT_ID.getValue}` in (${conformanceVariantIds.mkString(",")})"
    }
  }

  def mkUnConformanceFilter(topicId: Int, sheetId: Int): String = {
    val conformanceVariantIds = getConformanceVariantIDs(topicId, sheetId)
    val builder = pqlService
      .newPQLBuilder(topicId, sheetId)
      .addColumn(s"`case_table`.`${PreHandlerVariableEnum.VARIANT_ID.getValue}`")
      .addColumn("count(*)")
    if (conformanceVariantIds.length != 0) {
      builder.addTopicFilter(
        s"`case_table`.`${PreHandlerVariableEnum.VARIANT_ID.getValue}` not in (${conformanceVariantIds
          .mkString(",")})")
    }
    val unConformanceVariantIds = builder.collect().map(_.getInt(0))
    if (unConformanceVariantIds.length == 0) {
      null
    } else {
      s"`case_table`.`${PreHandlerVariableEnum.VARIANT_ID.getValue}` in (${unConformanceVariantIds.mkString(",")})"
    }
  }

  private def createReasonTable(sheetID: Integer): String = {
    val value = reasonTableName(sheetID)
    val tableName = if (value.nonEmpty) {
      return value.head
    } else { ConformanceService.mkReasonTableName(sheetID) }
    val dataModelFile: DataModelFileDTO = topicSheetService.getTopicSheetFileBySheetId(sheetID)
    val topicID: Integer = dataModelFile.getTopicId
    // query all variant// query all variant
    val bpmnCode: String = cloudFileInfoService.getCodeBySheetId(sheetID)
    // query places、transition、edges  use python// query places、transition、edges  use python
    val bpmnParam: BpmnParam = BpmnUtilsPy.getBpmnToPetriNetParams(bpmnCode)
    ConformanceService.mkReasonTable(pqlService, topicID, sheetID, bpmnParam, tableName)
    tableName
  }

  private def createConformanceTable(sheetID: Integer): String = {
    val tableName = ConformanceService.conformanceTableName(sheetID)
    if (DataSource.isExistTable(tableName)) {
      return tableName
    }
    ConformanceService.conformanceTable(
      allowProcessService,
      sheetID,
      reasonTableCache.getUnchecked(sheetID))
  }

  private def makeCacheKey(
      topicID: Int,
      sheetID: Int,
      timeRangeFilter: String): ConformanceCacheKey = {
    ConformanceCacheKey(
      topicID,
      sheetID,
      topicFilterManager.getAllTopicFilters(topicID).asScala.sorted.mkString(","),
      timeRangeFilter)
  }

  def getUnConformanceList(
      topicID: Int,
      sheetID: Int,
      pageNum: Int,
      pageSize: Int,
      startTime: String,
      endTime: String): java.util.List[UnConformance] = {
    val timeRangeFilter = ConformanceService.buildTimeFilter(startTime, endTime)
    val key = makeCacheKey(topicID, sheetID, timeRangeFilter)
    val cache = resultCache.getUnchecked(key)
    cache.unConformance.getUnConformanceList
        .stream()
        .skip((pageNum - 1) * pageSize)
        .limit(pageSize)
        .collect(Collectors.toList[UnConformance])
  }

  def getAllowProcessList(topicID: Int, sheetID: Int): java.util.List[UnConformance] = {
    val timeRangeFilter = ConformanceService.buildTimeFilter(null, null)
    val key = makeCacheKey(topicID, sheetID, timeRangeFilter)
    val cache = resultCache.getUnchecked(key)
    cache.unConformance.getAllowUnconformanceList
  }

  def getConformsVariantFilter(topicId: Int, sheetId: Int): String = {
    val conformanceTableName = conformanceTableCache.get(sheetId)
    val builder = createBuilder(topicId, sheetId)
    val conformsList = builder
      .addColumn(s"${conformanceTableName}.variant")
      .addColumn("count(*)")
      .addTopicFilter(s"${conformanceTableName}.isConforms")
      .collect()
    if (conformsList.isEmpty) {
      null
    } else {
      conformsList
        .map(m => s"'${m.getString(0)}'")
        .mkString(",")
    }
  }

  def calcConditionKpi(
      topicId: Int,
      sheetId: Int,
      conditionFilters: Array[ConditionFilter],
      kpiExpression: String): Array[(String, String)] = {
    conditionFilters.map(element => {
      val rows = pqlService
        .newPQLBuilder(topicId, sheetId)
        .addColumn(kpiExpression)
        .addTopicFilter(element.unconformanceFilterSingle)
        .collect()
      if (rows.isEmpty || rows(0).isNullAt(0)) {
        (element.name, "--")
      } else {
        (element.name, rows.head.get(0).toString)
      }
    })
  }

  def buildConditionFilterExpression(
      topicId: Int,
      sheetId: Int,
      allowDisabled: Boolean = true): Array[ConditionFilter] = {
    val tuples = conditionCache.getUnchecked(sheetId)
    if (tuples.isEmpty) {
      Array()
    } else {
      // 设为允许流程的不再加入filter
      val conditionReasons = allowProcessService
        .getList(sheetId)
        .asScala
        .filter(f => ReasonTypeEnum.isConditionReason(f.getReasonType))
        .map(_.getConditionName)
      tuples
        .filter(f => if (allowDisabled) !conditionReasons.contains(f.name) else true)
        .map(m => {
          val likeProcess = if (m.sourceRef == null) {
            m.targetRef
          } else if (m.targetRef == null) {
            m.sourceRef
          } else {
            s"${m.sourceRef},${m.targetRef}"
          }
          val source = if (m.sourceRef == null) "any" else s"'${m.sourceRef}'"
          val target = if (m.targetRef == null) "any" else s"'${m.targetRef}'"
          ConditionFilter(
            m.name,
            s"case when `case_table`.`variant` like '%${likeProcess}%'  then  ${m.expression} else true end",
            s"case when `case_table`.`variant` like '%${likeProcess}%'  then not  ${m.expression} else true end",
            s"PROCESS EQUALS ${source} to ${target} and ${m.expression}",
            s"PROCESS EQUALS ${source} to ${target} and not ${m.expression}",
            !conditionReasons.contains(m.name))
        })
    }
  }

  private def createBuilder(
      topicID: Int,
      sheetID: Int,
      replaceAllowProcess: Boolean = true): PQLBuilder = {
    try {
      ConformanceService.createConformancePQLBuild(
        pqlService,
        topicID,
        sheetID,
        reasonTableCache.get(sheetID),
        conformanceTableCache.get(sheetID),
        allowProcessService,
        replaceAllowProcess)
    } catch {
      case e: RuntimeException if e.getMessage.contains("No path found") =>
        logWarn(s"Primary PQL builder creation failed, attempting fallback strategy for sheet $sheetID")
        createBuilderWithFallback(topicID, sheetID, replaceAllowProcess)
      case e: Exception =>
        logError(s"Failed to create PQL builder for sheet $sheetID", e)
        throw e
    }
  }

  private def createBuilderWithFallback(
      topicID: Int,
      sheetID: Int,
      replaceAllowProcess: Boolean): PQLBuilder = {
    logInfo(s"Using fallback PQL builder strategy for topicID=$topicID, sheetID=$sheetID")

    // 使用简化的连接策略，只连接到基础表
    val reasonTable = reasonTableCache.get(sheetID)
    val conformanceTable = conformanceTableCache.get(sheetID)

    val builder = pqlService.newPQLBuilder(topicID, sheetID)

    // 只注册到基础表，不建立表间直接连接
    val reasonDF = if (replaceAllowProcess) {
      val allowReasons = allowProcessService.getList(sheetID).asScala.map(_.getResult)
      if (allowReasons.nonEmpty) {
        val cols = SparkSessionEnv.getSparkSession
          .table(reasonTable)
          .schema
          .names
          .filterNot(_.equals("reason"))
          .map(col)
        SparkSessionEnv.getSparkSession
          .table(reasonTable)
          .select(
            cols ++ Seq(
              when(col("reason").isInCollection(allowReasons), "Conforms")
                .otherwise(col("reason"))
                .as("reason")): _*)
      } else {
        SparkSessionEnv.getSparkSession.table(reasonTable)
      }
    } else {
      SparkSessionEnv.getSparkSession.table(reasonTable)
    }

    builder.modelCatalog.registerTempPuView(
      reasonTable,
      reasonDF.localCheckpoint(),
      builder.modelDesc.baseVirtualCaseTable.tableName,
      Seq("variantId"),
      Seq("variantId"))

    builder.modelCatalog.registerTempPuView(
      conformanceTable,
      builder.sparkSession.table(conformanceTable),
      builder.modelDesc.baseVirtualCaseTable.tableName,
      Seq("variantId"),
      Seq("variantId"))

    logInfo(s"Fallback PQL builder created successfully for sheet $sheetID")
    builder
  }

  def getConformanceViewData(
      topicID: Int,
      sheetID: Int,
      reason: String,
      reasonType: Integer,
      timeFormat: String,
      startTime: String,
      endTime: String): ConformanceViewDataOutputVO = {
    val filterReason = if (reason == null) {
      "null"
    } else {
      reason
    }
    val timeRangeFilter = ConformanceService.buildTimeFilter(startTime, endTime)
    resultCache
      .get(makeCacheKey(topicID, sheetID, timeRangeFilter))
      .conformanceViewData(
        DiagramCacheKey(
          filterReason,
          reasonType,
          if (timeFormat == null) "yyyy-MM" else timeFormat))
  }

  def getConformanceKpi4General(
      topicID: Int,
      sheetID: Int,
      startTime: String,
      endTime: String): java.util.List[ConformanceKpi] = {
    val timeRangeFilter = ConformanceService.buildTimeFilter(startTime, endTime)
    val key = makeCacheKey(topicID, sheetID, timeRangeFilter)
    val cache = resultCache.getUnchecked(key)
    cache.kpi
  }

  def getConformanceKpi4General(
      topicID: Int,
      sheetID: Int,
      reason: String,
      reasonType: Int): java.util.List[ConformanceKpi] = {
    val conformanceTableName = conformanceTableCache.get(sheetID)
    val reasonTable = reasonTableCache.get(sheetID)
    val unConformanceFiler = if (ReasonTypeEnum.isConditionReason(reasonType)) {
      reason
    } else {
      s" ${reasonTable}.reason = '${reason}'"
    }
    getConformanceKpi4General(
      topicID,
      sheetID,
      reasonType,
      s"${conformanceTableName}.isConforms",
      unConformanceFiler,
      ConformanceService.buildTimeFilter(null, null))
  }

  def getConformanceKpi4General(
      topicID: Int,
      sheetID: Int,
      reasonType: Integer,
      conformanceFilter: String,
      unConformanceFilter: String,
      timeRangeFilter: String): java.util.List[ConformanceKpi] = {
    val conditionFilters = buildConditionFilterExpression(topicID, sheetID)
    val token = userInfoUtil.getUserInfo
    topicSheetKpiService
      .getTopicSheetKpiList(sheetID)
      .asScala
      .par
      .map { kpi =>
        UserInfoUtil.authInfo.set(token)
        val expression = buildKpiExpressionWithFirst(topicID, sheetID, kpi.getExpression)
        val builder = ConformanceKpi.builder
          .unit(dataApiClient.formatToPQL(topicID, sheetID, kpi.getUnit))
          .name(dataApiClient.formatRefer(topicID, kpi.getName))
          .originalName(kpi.getName)
          .originalUnit(kpi.getUnit)
          .kpiId(kpi.getId)
          .baseLine(kpi.getBaseLine)
          .kpiType(kpi.getType)
          .expression(kpi.getExpression)
          .topicSheetId(sheetID)
          .saveType(if (Objects.equals(kpi.getFromKnowledge, KpiFromKnowledgeEnum.FROM.getValue))
            KpiSaveTypeEnum.KNOWLEDGE_MODEL.getValue
          else KpiSaveTypeEnum.CUSTOMIZE.getValue)
          .initType(kpi.getType)
          .formatting(kpi.getFormatting)
          .format(kpi.getFormat)
          .columnType(kpi.getColumnType)
        val (conformanceData: String, unConformanceData: String) = mkKpi(
          topicID,
          sheetID,
          reasonType,
          conformanceFilter,
          unConformanceFilter,
          timeRangeFilter,
          conditionFilters,
          expression)
        builder
          .conformance(conformanceData)
          .unConformance(unConformanceData)
          .build()
      }
      .toList
      .asJava
  }

  private def mkKpi(
      topicID: Int,
      sheetID: Int,
      reasonType: Integer,
      conformanceFilter: String,
      unConformanceFilter: String,
      timeRangeFilter: String,
      conditionFilters: Array[ConditionFilter],
      expression: String) = {
    val conformsBuilder = createBuilder(topicID, sheetID)
    conformsBuilder.addColumn(expression)
    conformsBuilder.addTopicFilter(conformanceFilter)
    conditionFilters.foreach(tp => conformsBuilder.addTopicFilter(tp.conformanceFilter))
    conformsBuilder.addTopicFilter(timeRangeFilter)
    val head = conformsBuilder.collect().head
    val conformanceData = if (head.isNullAt(0)) {
      "NULL"
    } else {
      head.apply(0).toString
    }
    val conditionUnconformanceFilter = mkUnConformanceFilter(topicID, sheetID)
    val unConformsBuilder = createBuilder(topicID, sheetID, false)
    unConformsBuilder.addColumn(expression)
    unConformsBuilder.addTopicFilter(timeRangeFilter)
    if (reasonType != null) {
      // process equals * to * and not xxx
      unConformsBuilder.addTopicFilter(unConformanceFilter)
    }
    if (reasonType == null && conditionUnconformanceFilter != null) {
      unConformsBuilder.addTopicFilter(conditionUnconformanceFilter)
    }
    val head1 = unConformsBuilder.collect().head
    val unConformanceData = if (head1.isNullAt(0)) {
      "NULL"
    } else {
      head1.apply(0).toString
    }
    (conformanceData, unConformanceData)
  }

  def getConformanceGeneralData(
      topicID: Int,
      sheetID: Int,
      startTime: String,
      endTime: String): ConformanceGeneralData = {
    val timeRangeFilter = ConformanceService.buildTimeFilter(startTime, endTime)
    val key = makeCacheKey(topicID, sheetID, timeRangeFilter)
    val cache = resultCache.getUnchecked(key)
    cache.conformanceGeneralData
  }

  def queryCorrelation(
      topicID: Int,
      sheetID: Int,
      valueExpression: String,
      reason: String,
      reasonType: Integer,
      timeRangeFilter: String): (java.util.List[RelatedDTO], java.util.List[RelatedDTO]) = {
    val reasonTable: String = reasonTableCache.getUnchecked(sheetID)
    val reasonFilter = if (ReasonTypeEnum.isConditionReason(reasonType)) {
      reason
    } else {
      s"${reasonTable}.reason = '${reason}'"
    }
    val builder = createBuilder(topicID, sheetID)
      .addTopicFilter(reasonFilter)
      .addColumn(s"${reasonTable}.variantId")
    if (!timeRangeFilter.isEmpty) {
      builder.addTopicFilter(timeRangeFilter)
    }
    val rows = builder.collect()
    val filter = if (rows.isEmpty) {
      "false"
    } else {
      s"`case_table`.`${PreHandlerVariableEnum.VARIANT_ID.getValue}` in (${rows.map(_.getInt(0)).mkString(",")} )"
    }
    // 为了避免表膨胀  同时不用 filter
    // count(distinct ENCODED_CASE_ID_COLUMN())=0时，filter为null，过滤掉，不会影响web显示，count=0
    val pqlBuilder = createBuilder(topicID, sheetID)
      .addColumn(valueExpression)
      .addColumn("count(distinct ENCODED_CASE_ID_COLUMN())")
      .addColumn(filter, "filter_result")
    if (!timeRangeFilter.isEmpty) {
      pqlBuilder.addTopicFilter(timeRangeFilter)
    }
    val (hasCase, nonCase) = pqlBuilder
      .collect()
      .filter(f => !f.isNullAt(0) && !f.isNullAt(2))
      .map { row =>
        (
          row.getBoolean(2),
          RelatedDTO.builder().variantName(row.get(0).toString).num(row.getLong(1)).build())
      }
      .partition(_._1)
    (hasCase.map(_._2).toList.asJava, nonCase.map(_._2).toList.asJava)
  }

  def addReasonTopicFilter(
      topicID: Int,
      sheetID: Int,
      reason: String,
      reasonType: Integer): String = {
    if (ReasonTypeEnum.isConditionReason(reasonType)) {
      reason
    } else {
      val reasonTable = reasonTableCache.get(sheetID)
      val builder = createBuilder(topicID, sheetID, false)
      builder.addColumn(s"array_join(collect_set(${reasonTable}.variantId), ',') ")
      builder.addTopicFilter(s"${reasonTable}.reason = '${reason}' ")
      val rows = builder.collect().head
      if (rows.isNullAt(0) || rows.get(0) == "") {
        "false"
      } else {
        s"`case_table`.`${PreHandlerVariableEnum.VARIANT_ID.getValue}` in (${rows.getString(0)})"
      }
    }
  }

  def buildKpiExpressionWithFirst(
      topicId: Integer,
      sheetId: Integer,
      expression: String): String = {
    if (DimensionQuery.checkHasAggregationFunction(topicId, sheetId, expression, pqlService)) {
      expression
    } else {
      s"first(${expression})"
    }
  }
}

object ConformanceService {

  val CONFORMANCE_DB_NAME = "conformance_db"

  def mkReasonTable(
      pqlService: PQLService,
      topicID: Int,
      sheetID: Int,
      bpmn: BpmnParam,
      tableName: String): Unit = {
    val petriNet = toPetriNet(bpmn)
    val tmpTableName = s"${CONFORMANCE_DB_NAME}.`${UUID.randomUUID.toString.replace("-", "")}`"
    DataSource.createDatabaseIfNotExists(CONFORMANCE_DB_NAME);
    val pqlBuilder = pqlService.newPQLBuilder(topicID, sheetID, false)
    pqlBuilder.addColumn("variant_table.variant", "variant")
    pqlBuilder.addColumn("variant_table.variant_Id", "variantId")
    pqlBuilder.runWithRetry[Unit] { () =>
      val conf = SparkSessionEnv.getSparkSession.sessionState.conf
      SparkSessionEnv.getSparkSession.sessionState.catalogManager
        .setCurrentCatalog("spark_catalog")
      conf.setLocalProperty("spark.sql.pql.sourceTarget.encoding.enabled", "false")
      conf.setLocalProperty("spark.sql.pql.planCacheEnabled", "false")
      conf.setLocalProperty("spark.sql.pql.starryFunctionEnabled", "false")
      val frame = pqlBuilder
        .resultDF()
        .repartition(
          SparkSessionEnv.getSparkSession.sparkContext.defaultParallelism / 2 + 1,
          col("variantId"))
        .select(Seq(
          new Column(new Conformance(
            split(col("variant"), ",").expr,
            petriNet.getPlaces.toSet,
            petriNet.getTransitionSet.toSet,
            petriNet.getTmpTransitionSet.toSet,
            petriNet.getEventToTransition.map(tp => (tp._1, tp._2.toSet)).toMap,
            petriNet.getProduceMap.map(tp => (tp._1, tp._2.toSet)).toMap,
            petriNet.getConsumerMap.map(tp => (tp._1, tp._2.toSet)).toMap,
            petriNet.getStartPlaces.toSet,
            petriNet.getEndPlaces.toSet,
            true)).as("result"),
          col("variantId"),
          col("variant")): _*)
        .write
        .bucketBy(
          SparkSessionEnv.getSparkSession.sparkContext.defaultParallelism / 2 + 1,
          "variantId")
        .saveAsTable(tmpTableName)
    }

    pqlService
      .newPQLBuilder(topicID, sheetID, false)
      .runWithRetry[Unit](() => {
        SparkSessionEnv.getSparkSession.sessionState.catalogManager
          .setCurrentCatalog("spark_catalog")
        SparkSessionEnv.getSparkSession
          .table(tmpTableName)
          .select(Seq(explode(col("result")), col("variantId"), col("variant")): _*)
          .select(col("col").as("reason"), col("variantId"), col("variant"))
          .dropDuplicates(Seq("reason", "variantId", "variant"))
          .repartition(pqlBuilder.modelDesc.bucketNumber, col("reason"))
          .write
          .bucketBy(pqlBuilder.modelDesc.bucketNumber, "reason")
          .saveAsTable(s"${tableName}")
        DataSource.deleteTable(tmpTableName)
        DataSource.analyzeTable(SparkSessionEnv.getSparkSession, tableName)
      })

  }

  def conformanceTable(
      allowProcessService: AllowProcessService,
      sheetID: Int,
      reasonTable: String): String = {
    val tableName = s"${CONFORMANCE_DB_NAME}.`${UUID.randomUUID.toString.replace("-", "")}`"
    val allowReasons = allowProcessService.getList(sheetID).asScala.map(_.getResult)
    SparkSessionEnv.getSparkSession.sessionState.catalogManager.setCurrentCatalog("spark_catalog")
    val df = if (allowReasons.isEmpty) {
      SparkSessionEnv.getSparkSession.table(reasonTable)
    } else {
      val cols = SparkSessionEnv.getSparkSession
        .table(reasonTable)
        .schema
        .names
        .filterNot(_.equals("reason"))
        .map(col)
      val dbAndTable = UnresolvedAttribute.parseAttributeName(reasonTable)
      val df = SparkSessionEnv.getSparkSession
        .table(reasonTable)
        .select(
          cols ++ Seq(
            when(col("reason").isInCollection(allowReasons), "Conforms")
              .otherwise(col("reason"))
              .as("reason")): _*)
      PQLFunctions.as(dbAndTable.last, dbAndTable.head, df)
    }
    df.groupBy("variantId", Seq("variant"): _*)
      .agg(collect_list("reason").as("result"))
      .select(Seq(
        col("variantId"),
        col("variant"),
        when(
          size(array_distinct(col("result")))
            .equalTo(lit(1))
            .and(element_at(array_distinct(col("result")), 1).equalTo("Conforms")),
          Literal.TrueLiteral).otherwise(Literal.FalseLiteral).as("isConforms")): _*)
      .repartition(4, col("variantId"))
      .write
      .bucketBy(4, "variantId")
      .saveAsTable(s"${tableName}")
    tableName
  }

  def toPetriNet(bpmn: BpmnParam): PetriNet = {
    val eventToTransitions = bpmn.getTransitions.asScala
      .filterNot(_.get(1).equals("null"))
      .map(list => (UTF8String.fromString(list.get(1)), UTF8String.fromString(list.get(0))))
      .groupBy(_._1)
      .map(tp => (tp._1, tp._2.map(_._2).toSet))
      .toMap

    val transitions = bpmn.getTransitions.asScala
      .map(t => UTF8String.fromString(t.get(0)))
      .toSet
    val tmpTransitions = bpmn.getTransitions.asScala
      .filter(_.get(1).equals("null"))
      .map(t => UTF8String.fromString(t.get(0)))
      .toSet
    val places = bpmn.getPlaces.asScala.map(UTF8String.fromString).toSet

    val produceMap =
      bpmn.getEdges.asScala
        .map(tp => (UTF8String.fromString(tp.get(0)), UTF8String.fromString(tp.get(1))))
        .filter(tp => places.contains(tp._2))
        .groupBy(_._1)
        .map(pair => (pair._1, pair._2.map(_._2).toSet))
    val consumerMap = bpmn.getEdges.asScala
      .map(tp => (UTF8String.fromString(tp.get(1)), UTF8String.fromString(tp.get(0))))
      .filter(tp => places.contains(tp._2))
      .groupBy(_._1)
      .map(pair => (pair._1, pair._2.map(_._2).toSet))

    val start = Set(UTF8String.fromString("source"))

    val end = Set(UTF8String.fromString("sink"))

    new PetriNet(
      places,
      transitions,
      tmpTransitions,
      eventToTransitions,
      produceMap,
      consumerMap,
      start,
      end)
  }

  def createConformancePQLBuild(
      pqlService: PQLService,
      topicID: Int,
      sheetID: Int,
      reasonTable: String,
      conformance: String,
      allowProcessService: AllowProcessService,
      replaceAllowProcess: Boolean): PQLBuilder = {
    try {
      val allowReasons = allowProcessService.getList(sheetID).asScala.map(_.getResult)
      val df = if (allowReasons.isEmpty || !replaceAllowProcess) {
        SparkSessionEnv.getSparkSession.table(reasonTable)
      } else {
        val cols = SparkSessionEnv.getSparkSession
          .table(reasonTable)
          .schema
          .names
          .filterNot(_.equals("reason"))
          .map(col)
        val dbAndTable = UnresolvedAttribute.parseAttributeName(reasonTable)
        val df = SparkSessionEnv.getSparkSession
          .table(reasonTable)
          .select(
            cols ++ Seq(
              when(col("reason").isInCollection(allowReasons), "Conforms")
                .otherwise(col("reason"))
                .as("reason")): _*)
        PQLFunctions.as(dbAndTable.last, dbAndTable.head, df)
      }

      val builder = pqlService.newPQLBuilder(topicID, sheetID)

      // 验证表是否存在
      validateTableExists(reasonTable, "reason table")
      validateTableExists(conformance, "conformance table")

      // 注册reasonTable到baseVirtualCaseTable，使用处理过的df
      builder.modelCatalog.registerTempPuView(
        reasonTable,
        df.localCheckpoint(),
        builder.modelDesc.baseVirtualCaseTable.tableName,
        Seq("variantId"),
        Seq("variantId"))

      // 注册conformance表到baseVirtualCaseTable
      builder.modelCatalog.registerTempPuView(
        conformance,
        builder.sparkSession.table(conformance),
        builder.modelDesc.baseVirtualCaseTable.tableName,
        Seq("variantId"),
        Seq("variantId"))

      // 建立reasonTable和conformance表之间的直接连接关系
      // 这确保了两个表之间有明确的连接路径
      builder.modelCatalog.registerTempPuView(
        reasonTable,
        df.localCheckpoint(),
        conformance,
        Seq("variantId"),
        Seq("variantId"))

      logInfo(s"Successfully registered PQL views for topicID=$topicID, sheetID=$sheetID")
      logInfo(s"ReasonTable: $reasonTable, ConformanceTable: $conformance")

      builder
    } catch {
      case e: Exception =>
        logError(s"Failed to create conformance PQL builder for topicID=$topicID, sheetID=$sheetID", e)
        throw new RuntimeException(s"Failed to setup PQL builder for conformance analysis: ${e.getMessage}", e)
    }
  }

  private def validateTableExists(tableName: String, tableType: String): Unit = {
    try {
      SparkSessionEnv.getSparkSession.table(tableName).schema
      logInfo(s"Validated $tableType exists: $tableName")
    } catch {
      case e: Exception =>
        logError(s"$tableType does not exist: $tableName", e)
        throw new RuntimeException(s"Required $tableType '$tableName' does not exist", e)
    }
  }

  def mkReasonTableName(sheetID: Int): String = {
    s"${CONFORMANCE_DB_NAME}.`reason_${sheetID}`"
  }

  def reasonTableName(sheetID: Int): Seq[String] = {
    ConformanceUtils.reasonTableName(sheetID)
  }

  def conformanceTableName(sheetID: Int): String = {
    s"${CONFORMANCE_DB_NAME}.`conformance_${sheetID}`"
  }

  def calUpDown(subtract: BigDecimal) =
    if (subtract.compareTo(BigDecimal.ZERO) > 0) KpiUpDownEnum.UP.getValue
    else if (subtract.compareTo(BigDecimal.ZERO) < 0) KpiUpDownEnum.DOWN.getValue
    else null

  def buildTimeFilter(startTime: String, endTime: String): String = {
    val timeColumn = s"`case_table`.`${PreHandlerVariableEnum.START_TIME.getValue}`"
    val startFilter = if (!isEmpty(startTime)) {
      s"$timeColumn >= '$startTime'"
    } else {
      " true "
    }
    val endFilter = if (!isEmpty(endTime)) {
      s" and $timeColumn <= '$endTime'"
    } else {
      " and true "
    }
    startFilter + endFilter
  }

  def completedDate(dates: Array[String], format: String): Array[String] = {
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    val formatToDay = format == "yyyy-MM-dd"
    val dateObjects = dates.map(
      date =>
        if (formatToDay) LocalDate.parse(date, formatter)
        else LocalDate.parse(date + "-01", formatter))
    implicit val localDateOrdering: Ordering[LocalDate] = Ordering.fromLessThan(_ isBefore _)
    val minDate = dateObjects.min
    val maxDate = dateObjects.max
    val allDates = Iterator
      .iterate(minDate)(m => if (formatToDay) m.plusDays(1) else m.plusMonths(1))
      .takeWhile(!_.isAfter(maxDate))
    val completedDates = allDates
      .map(f => {
        val date = f.format(formatter)
        if (formatToDay) date else date.take(7)
      })
      .toArray
    completedDates.sorted
  }
}
